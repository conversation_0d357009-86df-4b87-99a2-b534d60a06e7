import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Heart, Reply, Edit, Trash2, MoreHorizontal } from 'lucide-react';
import { BlogCommentData } from '@/services/blogCommentService';
import { useAuth } from '@/lib/AuthContext';
import { formatDate } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { useConfirmDialog } from '@/hooks/useConfirmDialog';

interface BlogCommentItemProps {
  comment: BlogCommentData;
  onReply: (parentId: string, content: string) => Promise<void>;
  onLike: (commentId: string) => Promise<void>;
  onEdit: (commentId: string, content: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  isReply?: boolean;
  currentUserId?: string | undefined;
}

// Backend URL for static assets
const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

const BlogCommentItem: React.FC<BlogCommentItemProps> = ({
  comment,
  onReply,
  onLike,
  onEdit,
  onDelete,
  isReply = false,
  currentUserId
}) => {
  const { dialogState, showConfirm, hideConfirm, setLoading } = useConfirmDialog();

  const { user, isAuthenticated } = useAuth();
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [editContent, setEditContent] = useState(comment.content);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showReplies, setShowReplies] = useState(false);

  // Helper function to get complete image URL
  const getImageUrl = (path: string) => {
    if (!path) return '/images/default-avatar.png';
    if (path.startsWith('http')) return path;
    if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
    return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
  };

  const isCommentOwner = user && comment.author._id === user.id;
  const isLikedByUser = user && comment.likedBy.includes(user.id);

  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!replyContent.trim()) return;

    setIsSubmitting(true);
    try {
      await onReply(comment._id, replyContent);
      setReplyContent('');
      setShowReplyForm(false);
      setShowReplies(true); // Show replies after posting
      toast({
        title: "Reply posted",
        description: "Your reply has been posted successfully",
      });
    } catch (error) {
      toast({
        title: "Failed to post reply",
        description: "Please try again",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editContent.trim()) return;

    setIsSubmitting(true);
    try {
      await onEdit(comment._id, editContent);
      setShowEditForm(false);
      toast({
        title: "Comment updated",
        description: "Your comment has been updated successfully",
      });
    } catch (error) {
      toast({
        title: "Failed to update comment",
        description: "Please try again",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLike = async () => {
    if (!isAuthenticated) {
      toast({
        title: "Login required",
        description: "Please login to like comments",
        variant: "destructive",
      });
      return;
    }

    try {
      await onLike(comment._id);
    } catch (error) {
      toast({
        title: "Failed to like comment",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const handleDelete = () => {
    showConfirm({
      title: "Delete Comment",
      message: "Are you sure you want to delete this comment?",
      confirmText: "Delete",
      cancelText: "Cancel",
      variant: "destructive",
      onConfirm: performDelete,
    });
  };

  const performDelete = async () => {
    try {
      setLoading(true);
      await onDelete(comment._id);
      toast({
        title: "Comment deleted",
        description: "Your comment has been deleted",
      });
      hideConfirm();
    } catch (error) {
      toast({
        title: "Failed to delete comment",
        description: "Please try again",
        variant: "destructive",
      });
      setLoading(false);
    }
  };

  if (comment.status === 'deleted') {
    return (
      <div className={`${isReply ? 'ml-6 sm:ml-12' : ''} p-3 sm:p-4 bg-muted/50 rounded-xl border border-border`}>
        <p className="text-muted-foreground italic text-sm sm:text-base">[This comment has been deleted]</p>
      </div>
    );
  }

  return (
    <div className={`${isReply ? 'ml-6 sm:ml-12 border-l-2 border-border pl-3 sm:pl-4' : ''}`}>
      <div className="bg-card rounded-xl p-4 sm:p-6 shadow-sm border border-border transition-all duration-300 hover:shadow-md">
        <div className="flex items-start justify-between">
          <div className="flex items-start flex-1 min-w-0">
            <Avatar className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 ring-2 ring-border flex-shrink-0">
              <AvatarImage
                src={getImageUrl(comment.author.avatar)}
                alt={comment.author.name}
              />
              <AvatarFallback className="bg-muted text-muted-foreground font-medium text-xs sm:text-sm">
                {comment.author.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>

            <div className="ml-2 sm:ml-3 lg:ml-4 flex-1 min-w-0">
              {/* Mobile Layout */}
              <div className="block sm:hidden">
                <div className="flex items-center gap-2 mb-1">
                  <a
                    href={`/author/@${comment.author.username}`}
                    className="font-semibold text-sm text-foreground hover:text-brand-600 transition-colors truncate"
                  >
                    {comment.author.name}
                  </a>
                  {comment.author.isVerified && (
                    <div className="w-5 h-5 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 48 48">
                        <defs>
                          <linearGradient id={`mobile-grad-${comment._id}`} x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse">
                            <stop offset="0" stopColor="#2aa4f4"></stop>
                            <stop offset="1" stopColor="#007ad9"></stop>
                          </linearGradient>
                        </defs>
                        <path fill={`url(#mobile-grad-${comment._id})`} d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.910,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.920,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.920-0.825c0.710-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path>
                        <path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.920-0.316l-4.706-3.660c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.410-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.410l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                      </svg>
                    </div>
                  )}
                </div>
                {comment.author.roleTitle && (
                  <p className="text-xs text-muted-foreground mb-1 truncate">{comment.author.roleTitle}</p>
                )}
                <span className="text-xs text-muted-foreground">
                  {formatDate(comment.createdAt)}
                  {comment.isEdited && (
                    <span className="ml-1">(edited)</span>
                  )}
                </span>
              </div>

              {/* Desktop Layout */}
              <div className="hidden sm:flex sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                <div className="flex items-center gap-2">
                  <a
                    href={`/author/@${comment.author.username}`}
                    className="font-semibold text-base text-foreground hover:text-brand-600 transition-colors truncate"
                  >
                    {comment.author.name}
                  </a>
                  {comment.author.isVerified && (
                    <div className="w-6 h-6 flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="24" height="24" viewBox="0 0 48 48">
                        <defs>
                          <linearGradient id={`desktop-grad-${comment._id}`} x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse">
                            <stop offset="0" stopColor="#2aa4f4"></stop>
                            <stop offset="1" stopColor="#007ad9"></stop>
                          </linearGradient>
                        </defs>
                        <path fill={`url(#desktop-grad-${comment._id})`} d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.910,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.920,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.920-0.825c0.710-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path>
                        <path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.920-0.316l-4.706-3.660c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.410-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.410l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                      </svg>
                    </div>
                  )}
                </div>
                <span className="text-sm text-muted-foreground">
                  {formatDate(comment.createdAt)}
                  {comment.isEdited && (
                    <span className="ml-1 text-xs">(edited)</span>
                  )}
                </span>
              </div>

              {comment.author.roleTitle && (
                <p className="hidden sm:block text-sm text-muted-foreground mb-2 truncate">{comment.author.roleTitle}</p>
              )}

              {showEditForm ? (
                <form onSubmit={handleEditSubmit} className="mt-2 sm:mt-3">
                  <Textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="mb-3 text-sm sm:text-base"
                    rows={3}
                  />
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                      type="submit"
                      size="sm"
                      disabled={isSubmitting || !editContent.trim()}
                      className="bg-brand-600 hover:bg-brand-700 text-white w-full sm:w-auto text-xs sm:text-sm"
                    >
                      {isSubmitting ? 'Updating...' : 'Update'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowEditForm(false);
                        setEditContent(comment.content);
                      }}
                      className="w-full sm:w-auto text-xs sm:text-sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              ) : (
                <p className="text-foreground mt-2 leading-relaxed text-sm sm:text-base break-words">
                  {comment.content}
                </p>
              )}

              <div className="flex flex-wrap items-center gap-2 sm:gap-4 mt-3 sm:mt-4 text-xs sm:text-sm text-muted-foreground">
                <button
                  onClick={handleLike}
                  className={`flex items-center gap-1 hover:text-red-500 transition-colors p-1 rounded ${isLikedByUser ? 'text-red-500' : ''
                    }`}
                  disabled={!isAuthenticated}
                >
                  <Heart
                    size={14}
                    className={`sm:w-4 sm:h-4 ${isLikedByUser ? 'fill-current' : ''}`}
                  />
                  {comment.likes}
                </button>

                {isAuthenticated && !isReply && (
                  <button
                    onClick={() => setShowReplyForm(!showReplyForm)}
                    className="flex items-center gap-1 hover:text-brand-600 transition-colors p-1 rounded"
                  >
                    <Reply size={14} className="sm:w-4 sm:h-4" />
                    Reply
                  </button>
                )}

                {comment.replies && comment.replies.length > 0 && (
                  <button
                    onClick={() => setShowReplies(!showReplies)}
                    className="flex items-center gap-1 hover:text-brand-600 transition-colors p-1 rounded"
                  >
                    {showReplies ? 'Hide' : 'Show'} {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
                  </button>
                )}
              </div>
            </div>
          </div>

          {isCommentOwner && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0 text-muted-foreground hover:text-foreground flex-shrink-0 ml-2">
                  <MoreHorizontal size={14} className="sm:w-4 sm:h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-card border-border">
                <DropdownMenuItem onClick={() => setShowEditForm(true)} className="hover:bg-muted text-xs sm:text-sm">
                  <Edit size={14} className="mr-2 sm:w-4 sm:h-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDelete} className="text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 text-xs sm:text-sm">
                  <Trash2 size={14} className="mr-2 sm:w-4 sm:h-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Reply form */}
        {showReplyForm && isAuthenticated && (
          <div className="mt-4 sm:mt-6 ml-8 sm:ml-12 lg:ml-16">
            <form onSubmit={handleReplySubmit}>
              <Textarea
                placeholder="Write a reply..."
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                className="mb-3 text-sm sm:text-base"
                rows={3}
              />
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  type="submit"
                  size="sm"
                  disabled={isSubmitting || !replyContent.trim()}
                  className="bg-brand-600 hover:bg-brand-700 text-white w-full sm:w-auto text-xs sm:text-sm"
                >
                  {isSubmitting ? 'Posting...' : 'Post Reply'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowReplyForm(false);
                    setReplyContent('');
                  }}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        )}
      </div>

      {/* Replies */}
      {showReplies && comment.replies && comment.replies.length > 0 && (
        <div className="mt-3 sm:mt-4 space-y-3 sm:space-y-4">
          {comment.replies.map((reply) => (
            <BlogCommentItem
              key={reply._id}
              comment={reply}
              onReply={onReply}
              onLike={onLike}
              onEdit={onEdit}
              onDelete={onDelete}
              isReply={true}
              currentUserId={currentUserId}
            />
          ))}
        </div>
      )}

      {/* Confirm Dialog */}
      <ConfirmDialog
        open={dialogState.open}
        onOpenChange={hideConfirm}
        title={dialogState.title}
        message={dialogState.message}
        onConfirm={dialogState.onConfirm}
        confirmText={dialogState.confirmText || 'Confirm'}
        cancelText={dialogState.cancelText || 'Cancel'}
        variant={dialogState.variant || 'default'}
        loading={dialogState.loading || false}
      />
    </div>
  );
};

export default BlogCommentItem; 